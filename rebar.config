%% ex: ts=4 sw=4 noexpandtab syntax=erlang

{xref_checks, [undefined_function_calls,
               undefined_functions,
               locals_not_used,
               deprecated_function_calls,
               deprecated_functions]}.


{deps, [
  {bson, {git, "https://github.com/comtihon/bson-erlang.git", {tag, "v0.2.2"}}},
  {pbkdf2, {git, "https://github.com/emqx/erlang-pbkdf2.git", {tag, "2.0.3"}}},
  {poolboy, {git, "https://github.com/comtihon/poolboy.git", {branch, "master"}}}
]}.

{profiles, [
    {test, [
        {erl_opts, [debug_info, {src_dirs, ["src", "test/eunit"]}]}
    ]}
]}.
