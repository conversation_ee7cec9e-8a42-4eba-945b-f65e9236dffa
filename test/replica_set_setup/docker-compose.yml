# Docker MongoDB replica set setup which is inspiered by the following article:
# https://www.sohamkamani.com/docker/mongo-replica-set/

services:
  mongo1:
    container_name: mongo1
    hostname: mongo1
    image: mongo:6
    expose:
      - 27017
    ports:
      - 30001:27017 
    command: mongod --replSet my-mongo-set
  mongo2:
    container_name: mongo2
    hostname: mongo2
    image: mongo:6
    expose:
      - 27017
    ports:
      - 30002:27017
    command: mongod --replSet my-mongo-set
  mongo3:
    container_name: mongo3
    hostname: mongo3
    image: mongo:6
    expose:
      - 27017
    ports:
      - 30003:27017
    command: mongod --replSet my-mongo-set
        
  # Initialize the replica set
  init_replica_set_container:
    container_name: init_replica_set_container
    image: mongo:6
    restart: "no"
    depends_on:
      - mongo1
      - mongo2
      - mongo3
    volumes:
      - ./data:/mydata
    entrypoint: ["bash", "-c"]
    command: >
      'sleep 10 ;
      echo executing mongosh command;
      mongosh --host mongo1:27017 --eval 
      "
      config = {
      \"_id\" : \"my-mongo-set\",
      \"members\" : [
        {
          \"_id\" : 0,
          \"host\" : \"mongo1:27017\"
        },
        {
          \"_id\" : 1,
          \"host\" : \"mongo2:27017\"
        },
        {
          \"_id\" : 2,
          \"host\" : \"mongo3:27017\"
        }
      ]
      };
      rs.initiate(config);
      " &&
      ls /mydata &&
      touch /mydata/replica_set_initialized
      &&
      chmod 777 /mydata/replica_set_initialized
      '
