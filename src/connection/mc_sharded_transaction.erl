%%%-------------------------------------------------------------------
%%% <AUTHOR> team
%%% @copyright (C) 2024, mongodb-erlang
%%% @doc
%%% MongoDB sharded cluster transaction support module.
%%% Handles shard key validation, mongos routing, and cross-shard coordination.
%%% @end
%%%-------------------------------------------------------------------
-module(mc_sharded_transaction).
-author("mongodb-erlang team").

-include("mongo_types.hrl").
-include("mongo_protocol.hrl").

%% API
-export([
  validate_shard_key/3,
  get_target_shard/3,
  validate_cross_shard_transaction/2,
  get_mongos_list/1,
  select_optimal_mongos/2,
  check_shard_compatibility/2,
  get_shard_collection_info/2,
  validate_transaction_operations/2
]).

-define(SHARD_KEY_CACHE_TTL, 300000). % 5 minutes
-define(MONGOS_HEALTH_CHECK_INTERVAL, 30000). % 30 seconds

%%%===================================================================
%%% API Functions
%%%===================================================================

%% @doc Validate that operations include required shard keys
-spec validate_shard_key(pid(), collection(), map() | bson:document()) -> 
  ok | {error, {missing_shard_key, list()}}.
validate_shard_key(Worker, Collection, Document) ->
  case get_shard_collection_info(Worker, Collection) of
    {ok, #{<<"key">> := ShardKey}} ->
      validate_document_shard_key(Document, ShardKey);
    {error, collection_not_sharded} ->
      ok; % Non-sharded collections don't require shard keys
    {error, Reason} ->
      {error, Reason}
  end.

%% @doc Get target shard for a document based on shard key
-spec get_target_shard(pid(), collection(), map() | bson:document()) -> 
  {ok, binary()} | {error, term()}.
get_target_shard(Worker, Collection, Document) ->
  case get_shard_collection_info(Worker, Collection) of
    {ok, ShardInfo} ->
      calculate_target_shard(Document, ShardInfo);
    Error ->
      Error
  end.

%% @doc Validate that all operations in a transaction can be executed together
-spec validate_cross_shard_transaction(pid(), list()) -> ok | {error, term()}.
validate_cross_shard_transaction(Worker, Operations) ->
  try
    ShardTargets = [get_operation_shard_target(Worker, Op) || Op <- Operations],
    UniqueShards = lists:usort([Shard || {ok, Shard} <- ShardTargets]),
    
    case length(UniqueShards) of
      0 -> {error, no_valid_operations};
      1 -> ok; % Single shard transaction
      _ -> 
        % Multi-shard transaction - check if supported
        case check_multi_shard_support(Worker) of
          true -> ok;
          false -> {error, multi_shard_transactions_not_supported}
        end
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%% @doc Get list of available mongos instances
-spec get_mongos_list(pid()) -> {ok, list()} | {error, term()}.
get_mongos_list(Worker) ->
  try
    Command = {<<"isMaster">>, 1},
    case mc_worker_api:command(Worker, Command) of
      {true, #{<<"msg">> := <<"isdbgrid">>, <<"hosts">> := Hosts}} ->
        {ok, Hosts};
      {true, #{<<"msg">> := <<"isdbgrid">>}} ->
        {ok, [get_current_host(Worker)]};
      _ ->
        {error, not_mongos}
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%% @doc Select optimal mongos based on load and health
-spec select_optimal_mongos(list(), map()) -> {ok, binary()} | {error, term()}.
select_optimal_mongos([], _Options) ->
  {error, no_mongos_available};
select_optimal_mongos([Mongos], _Options) ->
  {ok, Mongos};
select_optimal_mongos(MongosList, Options) ->
  try
    % Simple round-robin for now, can be enhanced with health checks
    Index = erlang:system_time(microsecond) rem length(MongosList),
    SelectedMongos = lists:nth(Index + 1, MongosList),
    {ok, SelectedMongos}
  catch
    _:_ ->
      {ok, hd(MongosList)} % Fallback to first mongos
  end.

%% @doc Check if sharded cluster supports required transaction features
-spec check_shard_compatibility(pid(), transaction_options()) -> ok | {error, term()}.
check_shard_compatibility(Worker, Options) ->
  try
    % Check MongoDB version across all shards
    case check_all_shards_version(Worker) of
      {ok, MinVersion} ->
        case is_version_compatible(MinVersion, Options) of
          true -> ok;
          false -> {error, {incompatible_version, MinVersion}}
        end;
      Error ->
        Error
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%% @doc Get shard collection information including shard key
-spec get_shard_collection_info(pid(), collection()) -> {ok, map()} | {error, term()}.
get_shard_collection_info(Worker, Collection) ->
  try
    % Check cache first
    case get_cached_shard_info(Collection) of
      {ok, Info} -> {ok, Info};
      cache_miss ->
        % Query config database
        Command = {<<"listCollections">>, 1, 
                  <<"filter">>, #{<<"name">> => Collection}},
        case mc_worker_api:command(Worker, Command) of
          {true, #{<<"cursor">> := #{<<"firstBatch">> := [CollInfo|_]}}} ->
            case maps:get(<<"options">>, CollInfo, #{}) of
              #{<<"shardKey">> := ShardKey} = Options ->
                Info = #{<<"key">> => ShardKey, <<"options">> => Options},
                cache_shard_info(Collection, Info),
                {ok, Info};
              _ ->
                {error, collection_not_sharded}
            end;
          _ ->
            {error, collection_not_found}
        end
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%% @doc Validate all operations in a transaction for shard compatibility
-spec validate_transaction_operations(pid(), list()) -> ok | {error, term()}.
validate_transaction_operations(Worker, Operations) ->
  try
    ValidationResults = [validate_operation(Worker, Op) || Op <- Operations],
    case [Error || {error, Error} <- ValidationResults] of
      [] -> ok;
      Errors -> {error, {validation_failed, Errors}}
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%%%===================================================================
%%% Internal Functions
%%%===================================================================

%% @doc Validate document contains required shard key fields
-spec validate_document_shard_key(map() | bson:document(), map()) -> 
  ok | {error, {missing_shard_key, list()}}.
validate_document_shard_key(Document, ShardKey) when is_map(Document) ->
  ShardKeyFields = maps:keys(ShardKey),
  DocumentFields = maps:keys(Document),
  MissingFields = ShardKeyFields -- DocumentFields,
  
  case MissingFields of
    [] -> ok;
    _ -> {error, {missing_shard_key, MissingFields}}
  end;
validate_document_shard_key(Document, ShardKey) when is_tuple(Document) ->
  % Convert BSON document to map for validation
  DocumentMap = maps:from_list(bson:fields(Document)),
  validate_document_shard_key(DocumentMap, ShardKey).

%% @doc Calculate target shard for a document
-spec calculate_target_shard(map() | bson:document(), map()) -> {ok, binary()} | {error, term()}.
calculate_target_shard(Document, #{<<"key">> := ShardKey} = ShardInfo) ->
  try
    % Extract shard key values from document
    ShardKeyValues = extract_shard_key_values(Document, ShardKey),
    
    % Calculate shard based on key values (simplified hash-based routing)
    ShardHash = calculate_shard_hash(ShardKeyValues),
    
    % Get shard list and select target
    case maps:get(<<"shards">>, ShardInfo, []) of
      [] -> {error, no_shards_available};
      Shards ->
        ShardIndex = ShardHash rem length(Shards),
        TargetShard = lists:nth(ShardIndex + 1, Shards),
        {ok, TargetShard}
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%% @doc Extract shard key values from document
-spec extract_shard_key_values(map() | bson:document(), map()) -> map().
extract_shard_key_values(Document, ShardKey) when is_map(Document) ->
  maps:with(maps:keys(ShardKey), Document);
extract_shard_key_values(Document, ShardKey) when is_tuple(Document) ->
  DocumentMap = maps:from_list(bson:fields(Document)),
  extract_shard_key_values(DocumentMap, ShardKey).

%% @doc Calculate hash for shard key values
-spec calculate_shard_hash(map()) -> integer().
calculate_shard_hash(ShardKeyValues) ->
  % Simple hash calculation - can be enhanced with MongoDB's actual algorithm
  KeyString = lists:flatten([io_lib:format("~p", [V]) || {_K, V} <- maps:to_list(ShardKeyValues)]),
  erlang:phash2(KeyString).

%% @doc Get operation's target shard
-spec get_operation_shard_target(pid(), map()) -> {ok, binary()} | {error, term()}.
get_operation_shard_target(Worker, #{collection := Collection, document := Document}) ->
  get_target_shard(Worker, Collection, Document);
get_operation_shard_target(Worker, #{collection := Collection, selector := Selector}) ->
  get_target_shard(Worker, Collection, Selector);
get_operation_shard_target(_Worker, _Operation) ->
  {error, invalid_operation}.

%% @doc Check if multi-shard transactions are supported
-spec check_multi_shard_support(pid()) -> boolean().
check_multi_shard_support(Worker) ->
  try
    Command = {<<"buildInfo">>, 1},
    case mc_worker_api:command(Worker, Command) of
      {true, #{<<"version">> := VersionBin}} ->
        Version = parse_version(VersionBin),
        is_multi_shard_supported(Version);
      _ ->
        false
    end
  catch
    _:_ -> false
  end.

%% @doc Parse MongoDB version string
-spec parse_version(binary()) -> {integer(), integer(), integer()}.
parse_version(VersionBin) ->
  VersionStr = binary_to_list(VersionBin),
  case string:tokens(VersionStr, ".") of
    [MajorStr, MinorStr | _] ->
      Major = list_to_integer(MajorStr),
      Minor = list_to_integer(MinorStr),
      {Major, Minor, 0};
    _ ->
      {0, 0, 0}
  end.

%% @doc Check if version supports multi-shard transactions
-spec is_multi_shard_supported({integer(), integer(), integer()}) -> boolean().
is_multi_shard_supported({Major, Minor, _}) when Major > 4; (Major =:= 4 andalso Minor >= 2) ->
  true;
is_multi_shard_supported(_) ->
  false.

%% @doc Check version compatibility with transaction options
-spec is_version_compatible({integer(), integer(), integer()}, transaction_options()) -> boolean().
is_version_compatible(Version, _Options) ->
  % For now, just check basic multi-shard support
  is_multi_shard_supported(Version).

%% @doc Check MongoDB version across all shards
-spec check_all_shards_version(pid()) -> {ok, {integer(), integer(), integer()}} | {error, term()}.
check_all_shards_version(Worker) ->
  try
    % Query config.shards to get all shard information
    Command = {<<"find">>, <<"shards">>},
    case mc_worker_api:command(Worker, Command) of
      {true, #{<<"cursor">> := #{<<"firstBatch">> := Shards}}} ->
        Versions = [get_shard_version(Worker, Shard) || Shard <- Shards],
        case [V || {ok, V} <- Versions] of
          [] -> {error, no_shard_versions};
          ValidVersions -> {ok, lists:min(ValidVersions)}
        end;
      _ ->
        {error, cannot_query_shards}
    end
  catch
    _:Reason ->
      {error, Reason}
  end.

%% @doc Get version of a specific shard
-spec get_shard_version(pid(), map()) -> {ok, {integer(), integer(), integer()}} | {error, term()}.
get_shard_version(_Worker, #{<<"host">> := _Host}) ->
  % Simplified - in real implementation, would connect to each shard
  {ok, {4, 2, 0}}. % Assume minimum supported version

%% @doc Get current host information
-spec get_current_host(pid()) -> binary().
get_current_host(_Worker) ->
  <<"localhost:27017">>. % Simplified implementation

%% @doc Validate a single operation
-spec validate_operation(pid(), map()) -> ok | {error, term()}.
validate_operation(Worker, #{collection := Collection, document := Document}) ->
  validate_shard_key(Worker, Collection, Document);
validate_operation(Worker, #{collection := Collection, selector := Selector}) ->
  validate_shard_key(Worker, Collection, Selector);
validate_operation(_Worker, _Operation) ->
  ok.

%% @doc Get cached shard information
-spec get_cached_shard_info(collection()) -> {ok, map()} | cache_miss.
get_cached_shard_info(_Collection) ->
  % Simplified - in real implementation, would use ETS or similar cache
  cache_miss.

%% @doc Cache shard information
-spec cache_shard_info(collection(), map()) -> ok.
cache_shard_info(_Collection, _Info) ->
  % Simplified - in real implementation, would store in ETS cache
  ok.
