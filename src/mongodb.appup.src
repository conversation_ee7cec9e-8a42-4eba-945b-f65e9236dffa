%% -*- mode: erlang -*-
{"3.0.25", [
   {"3.0.24", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.23", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.22", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.21", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.20", [
    {load_module, mc_worker_pid_info, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.19", [
    {load_module, mc_worker_pid_info, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.18", [
    {load_module, mc_worker_pid_info, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {<<"3.0.[0-7]">>, [
    {restart_application, mongodb}
   ]},
   {<<"3.0.([89]|10)">>, [
    {add_module, mc_util},
    {add_module, mc_worker_pid_info},
    {load_module, mc_auth_logic, brutal_purge, soft_purge, []},
    {load_module, mc_connection_man, brutal_purge, soft_purge, []},
    {load_module, mc_cursor, brutal_purge, soft_purge, []},
    {load_module, mc_worker_logic, brutal_purge, soft_purge, []},
    {load_module, mongo_protocol, brutal_purge, soft_purge, []},
    {load_module, mc_super_sup, brutal_purge, soft_purge, []},
    {load_module, mc_monitor, brutal_purge, soft_purge, []},
    {load_module, mc_server, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_topology_logics, brutal_purge, soft_purge, []},
    {load_module, mc_topology, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mongo_api, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []},
    {apply, {mc_super_sup, ensure_mc_worker_pid_info_server, []}}
  ]},
  {<<"3.0.1[1-7]">>, [
    {add_module, mc_worker_pid_info},
    {load_module, mc_auth_logic, brutal_purge, soft_purge, []},
    {load_module, mc_connection_man, brutal_purge, soft_purge, []},
    {load_module, mc_cursor, brutal_purge, soft_purge, []},
    {load_module, mc_worker_logic, brutal_purge, soft_purge, []},
    {load_module, mongo_protocol, brutal_purge, soft_purge, []},
    {load_module, mc_super_sup, brutal_purge, soft_purge, []},
    {load_module, mc_monitor, brutal_purge, soft_purge, []},
    {load_module, mc_server, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_util, brutal_purge, soft_purge, []},
    {load_module, mc_topology_logics, brutal_purge, soft_purge, []},
    {load_module, mc_topology, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mongo_api, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []},
    {apply, {mc_super_sup, ensure_mc_worker_pid_info_server, []}}
  ]},
  {<<".*">>, []}
 ],
 [
   {"3.0.24", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.23", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.22", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.21", [
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.20", [
    {load_module, mc_worker_pid_info, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.19", [
    {load_module, mc_worker_pid_info, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {"3.0.18", [
    {load_module, mc_worker_pid_info, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []}
   ]},
   {<<"3.0.[0-7]">>, [
    {restart_application, mongodb}
   ]},
   {<<"3.0.([89]|10)">>, [
    {load_module, mc_auth_logic, brutal_purge, soft_purge, []},
    {load_module, mc_connection_man, brutal_purge, soft_purge, []},
    {load_module, mc_cursor, brutal_purge, soft_purge, []},
    {load_module, mc_worker_logic, brutal_purge, soft_purge, []},
    {load_module, mongo_protocol, brutal_purge, soft_purge, []},
    {load_module, mc_super_sup, brutal_purge, soft_purge, []},
    {load_module, mc_monitor, brutal_purge, soft_purge, []},
    {load_module, mc_server, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_topology_logics, brutal_purge, soft_purge, []},
    {load_module, mc_topology, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mongo_api, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []},
    {delete_module, mc_util},
    {delete_module, mc_worker_pid_info}
  ]},
  {<<"3.0.1[1-7]">>, [
    {load_module, mc_auth_logic, brutal_purge, soft_purge, []},
    {load_module, mc_connection_man, brutal_purge, soft_purge, []},
    {load_module, mc_cursor, brutal_purge, soft_purge, []},
    {load_module, mc_worker_logic, brutal_purge, soft_purge, []},
    {load_module, mongo_protocol, brutal_purge, soft_purge, []},
    {load_module, mc_super_sup, brutal_purge, soft_purge, []},
    {load_module, mc_monitor, brutal_purge, soft_purge, []},
    {load_module, mc_server, brutal_purge, soft_purge, []},
    {load_module, mc_utils, brutal_purge, soft_purge, []},
    {load_module, mc_util, brutal_purge, soft_purge, []},
    {load_module, mc_topology_logics, brutal_purge, soft_purge, []},
    {load_module, mc_topology, brutal_purge, soft_purge, []},
    {load_module, mc_worker, brutal_purge, soft_purge, []},
    {load_module, mc_worker_api, brutal_purge, soft_purge, []},
    {load_module, mongo_api, brutal_purge, soft_purge, []},
    {load_module, mongoc, brutal_purge, soft_purge, []},
    {delete_module, mc_worker_pid_info}
  ]},
  {<<".*">>, []}
 ]
}.
