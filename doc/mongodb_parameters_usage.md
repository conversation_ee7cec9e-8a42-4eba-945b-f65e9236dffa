# MongoDB Parameters Usage Guide

This document describes the support for MongoDB connection and operation parameters in the mongodb-erlang driver.

## Supported Parameters

### 1. auth_source ✅ **Fully Supported**

The `auth_source` parameter specifies the database to authenticate against.

**Usage:**
```erlang
% Connect with auth_source
{ok, Connection} = mc_worker_api:connect([
    {database, <<"myapp">>},
    {login, <<"user">>},
    {password, <<"pass">>},
    {auth_source, <<"admin">>}  % Authenticate against admin database
]).

% For external authentication (LDAP, Kerberos)
{ok, Connection} = mc_worker_api:connect([
    {database, <<"myapp">>},
    {login, <<"<EMAIL>">>},
    {auth_source, <<"$external">>}
]).
```

**Default:** `<<"admin">>`

### 2. read_preference ✅ **Fully Supported**

The `read_preference` parameter controls which MongoDB instances are used for read operations.

**Supported modes:**
- `primary` - Read from primary only
- `secondary` - Read from secondary only  
- `primaryPreferred` - Read from primary, fallback to secondary
- `secondaryPreferred` - Read from secondary, fallback to primary
- `nearest` - Read from nearest instance

**Usage:**
```erlang
% Find with read preference
{ok, Cursor} = mc_worker_api:find(Connection, <<"collection">>, #{}, #{
    readopts => #{<<"mode">> => <<"secondary">>}
}).

% Find with read preference and tags
{ok, Cursor} = mc_worker_api:find(Connection, <<"collection">>, #{}, #{
    readopts => #{
        <<"mode">> => <<"secondary">>,
        <<"tags">> => [[{<<"datacenter">>, <<"east">>}]]
    }
}).

% Count with read preference
Count = mc_worker_api:count(Connection, <<"collection">>, #{}, #{
    readopts => #{<<"mode">> => <<"primaryPreferred">>}
}).
```

### 3. read_concern_level ✅ **Newly Added**

The `read_concern_level` parameter specifies the read concern level for read operations.

**Supported levels:**
- `local` - Return data from the instance with no guarantee
- `available` - Return data from the instance with no guarantee (for sharded clusters)
- `majority` - Return data acknowledged by majority of replica set members
- `linearizable` - Return data that reflects all successful majority-acknowledged writes
- `snapshot` - Return data from a snapshot of majority committed data

**Usage:**
```erlang
% Connect with read concern level
{ok, Connection} = mc_worker_api:connect([
    {database, <<"myapp">>},
    {read_concern_level, majority}
]).

% Find with specific read concern level
{ok, Cursor} = mc_worker_api:find(Connection, <<"collection">>, #{}, #{
    read_concern_level => linearizable
}).

% Count with read concern level
Count = mc_worker_api:count(Connection, <<"collection">>, #{}, #{
    read_concern_level => majority
}).
```

### 4. write_concern ✅ **Fully Supported**

The `write_concern` parameter controls the acknowledgment of write operations.

**Usage:**
```erlang
% Insert with write concern
WriteConcern = #{<<"w">> => 1, <<"j">> => true, <<"wtimeout">> => 5000},
{{true, _}, _} = mc_worker_api:insert(Connection, <<"collection">>, Document, WriteConcern).

% Update with write concern
WriteConcern = #{<<"w">> => <<"majority">>, <<"wtimeout">> => 10000},
{true, _} = mc_worker_api:update(Connection, <<"collection">>, Selector, Update, false, false, WriteConcern).

% Delete with write concern
WriteConcern = #{<<"w">> => 2, <<"wtimeout">> => 3000},
{true, _} = mc_worker_api:delete_limit(Connection, <<"collection">>, Selector, 1, WriteConcern).
```

### 5. write_concern_timeout ✅ **Supported via wtimeout**

The write concern timeout is supported through the `wtimeout` field in the write concern document.

**Usage:**
```erlang
% Write concern with timeout
WriteConcern = #{
    <<"w">> => <<"majority">>,
    <<"wtimeout">> => 5000  % 5 second timeout
},
{{true, _}, _} = mc_worker_api:insert(Connection, <<"collection">>, Document, WriteConcern).

% Different timeout values
WriteConcern1 = #{<<"w">> => 1, <<"wtimeout">> => 1000},   % 1 second
WriteConcern2 = #{<<"w">> => 2, <<"wtimeout">> => 10000},  % 10 seconds
WriteConcern3 = #{<<"w">> => <<"majority">>, <<"wtimeout">> => 30000}, % 30 seconds
```

## Complete Example

```erlang
% Connect with all parameters
{ok, Connection} = mc_worker_api:connect([
    {database, <<"myapp">>},
    {login, <<"user">>},
    {password, <<"password">>},
    {auth_source, <<"admin">>},
    {read_concern_level, majority}
]).

% Insert with write concern
WriteConcern = #{<<"w">> => <<"majority">>, <<"j">> => true, <<"wtimeout">> => 5000},
{{true, _}, _} = mc_worker_api:insert(Connection, <<"users">>, 
    #{<<"name">> => <<"John">>, <<"age">> => 30}, WriteConcern).

% Find with read preference and read concern
{ok, Cursor} = mc_worker_api:find(Connection, <<"users">>, 
    #{<<"age">> => #{<<"$gte">> => 18}}, #{
        readopts => #{<<"mode">> => <<"secondaryPreferred">>},
        read_concern_level => majority
    }).

% Count with read preference
Count = mc_worker_api:count(Connection, <<"users">>, #{}, #{
    readopts => #{<<"mode">> => <<"secondary">>}
}).
```

## Notes

- All parameters are optional and have sensible defaults
- `read_concern_level` was newly added in this implementation
- `write_concern_timeout` is implemented via the `wtimeout` field in write concern documents
- Read preferences support replica set tags for fine-grained control
- Write concerns support various acknowledgment levels including replica set tags
