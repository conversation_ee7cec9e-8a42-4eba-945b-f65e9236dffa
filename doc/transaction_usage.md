# MongoDB Transactions in Erlang Driver

This document describes how to use MongoDB transactions with the mongodb-erlang driver.

## Overview

MongoDB transactions provide ACID guarantees for multi-document operations. The mongodb-erlang driver supports transactions for:

- **Single Node**: MongoDB 4.2+ (multi-document transactions)
- **Replica Set**: MongoDB 4.0+ (multi-document transactions)
- **Sharded Cluster**: MongoDB 4.2+ (distributed transactions)

## Basic Usage

### Session Management

All transactions require a session. Sessions manage the transaction state and provide consistency guarantees.

```erlang
%% Start a session
{ok, SessionPid} = mc_worker_api:start_session(Connection).

%% Start a session with options
Options = #{timeout => 60000, implicit => false},
{ok, SessionPid} = mc_worker_api:start_session(Connection, Options).

%% End a session
ok = mc_worker_api:end_session(SessionPid).
```

### Transaction Lifecycle

#### Manual Transaction Management

```erlang
%% Start a transaction
{ok, SessionPid} = mc_worker_api:start_session(Connection),
{ok, TransactionPid} = mc_worker_api:start_transaction(SessionPid, Connection).

%% Perform operations within the transaction
TestDoc = #{<<"name">> => <<"John">>, <<"balance">> => 1000},
ok = mc_worker_api:in_transaction(TransactionPid, <<"accounts">>, insert, [TestDoc]).

%% Update within transaction
ok = mc_worker_api:in_transaction(TransactionPid, <<"accounts">>, update, 
    [#{<<"name">> => <<"John">>}, #{<<"$inc">> => #{<<"balance">> => -100}}]).

%% Commit the transaction
ok = mc_worker_api:commit_transaction(TransactionPid).

%% Clean up
ok = mc_worker_api:end_session(SessionPid).
```

#### Automatic Transaction Management

The `with_transaction` function provides automatic retry and error handling:

```erlang
{ok, SessionPid} = mc_worker_api:start_session(Connection),

Result = mc_worker_api:with_transaction(SessionPid, Connection,
    fun(TransactionPid) ->
        %% Transfer money between accounts
        ok = mc_worker_api:in_transaction(TransactionPid, <<"accounts">>, update,
            [#{<<"name">> => <<"Alice">>}, #{<<"$inc">> => #{<<"balance">> => -100}}]),
        
        ok = mc_worker_api:in_transaction(TransactionPid, <<"accounts">>, update,
            [#{<<"name">> => <<"Bob">>}, #{<<"$inc">> => #{<<"balance">> => 100}}]),
        
        <<"transfer_completed">>
    end),

{ok, <<"transfer_completed">>} = Result,
ok = mc_worker_api:end_session(SessionPid).
```

### Transaction Options

You can specify transaction options for read/write concerns and timeouts:

```erlang
Options = #transaction_options{
    read_concern = #{<<"level">> => <<"majority">>},
    write_concern = #{<<"w">> => <<"majority">>, <<"j">> => true},
    max_commit_time_ms = 30000
},

{ok, TransactionPid} = mc_worker_api:start_transaction(SessionPid, Connection, Options).
```

## High-Level API (mongo_api)

For topology-aware operations with automatic server selection:

```erlang
%% Connect to replica set or sharded cluster
{ok, Topology} = mongo_api:connect(
    replica_set,
    ["localhost:27017", "localhost:27018", "localhost:27019"],
    [{name, my_topology}],
    [{database, <<"mydb">>}]
).

%% Start session
{ok, SessionPid} = mongo_api:start_session(Topology).

%% Use with_transaction for automatic handling
Result = mongo_api:with_transaction(SessionPid, Topology,
    fun(_TransactionPid) ->
        %% Operations are automatically executed within transaction context
        mongo_api:insert(Topology, <<"collection1">>, #{<<"data">> => <<"value1">>}),
        mongo_api:insert(Topology, <<"collection2">>, #{<<"data">> => <<"value2">>}),
        <<"success">>
    end),

{ok, <<"success">>} = Result,
ok = mongo_api:end_session(SessionPid).
```

## Error Handling

### Transaction Conflicts

Transactions may fail due to conflicts. Use automatic retry with `with_transaction`:

```erlang
Result = mc_worker_api:with_transaction(SessionPid, Connection,
    fun(TransactionPid) ->
        try
            %% Your transaction operations here
            ok = mc_worker_api:in_transaction(TransactionPid, Collection, insert, [Doc]),
            <<"success">>
        catch
            error:{bad_query, {write_conflict, _}} ->
                %% This will trigger automatic retry
                throw(write_conflict)
        end
    end).
```

### Manual Error Handling

```erlang
{ok, TransactionPid} = mc_worker_api:start_transaction(SessionPid, Connection),

try
    %% Your operations
    ok = mc_worker_api:in_transaction(TransactionPid, Collection, insert, [Doc]),
    ok = mc_worker_api:commit_transaction(TransactionPid)
catch
    error:Reason ->
        %% Abort on error
        ok = mc_worker_api:abort_transaction(TransactionPid),
        {error, Reason}
end.
```

## Best Practices

### 1. Keep Transactions Short

```erlang
%% Good: Short transaction
mc_worker_api:with_transaction(SessionPid, Connection,
    fun(TransactionPid) ->
        mc_worker_api:in_transaction(TransactionPid, <<"accounts">>, update, [Selector, Update])
    end).

%% Avoid: Long-running transactions
%% Don't include network calls, file I/O, or complex computations
```

### 2. Use Appropriate Read/Write Concerns

```erlang
%% For critical data
Options = #transaction_options{
    read_concern = #{<<"level">> => <<"majority">>},
    write_concern = #{<<"w">> => <<"majority">>, <<"j">> => true}
}.

%% For performance-critical operations
Options = #transaction_options{
    read_concern = #{<<"level">> => <<"local">>},
    write_concern = #{<<"w">> => 1}
}.
```

### 3. Handle Session Lifecycle

```erlang
%% Always clean up sessions
{ok, SessionPid} = mc_worker_api:start_session(Connection),
try
    %% Your transaction operations
    Result = mc_worker_api:with_transaction(SessionPid, Connection, Fun)
after
    ok = mc_worker_api:end_session(SessionPid)
end.
```

### 4. Use Connection Pooling

```erlang
%% For high-throughput applications, use topology-aware connections
{ok, Topology} = mongo_api:connect(replica_set, Hosts, TopologyOpts, WorkerOpts),

%% Sessions can be reused across multiple transactions
{ok, SessionPid} = mongo_api:start_session(Topology),

%% Multiple transactions with the same session
lists:foreach(fun(Data) ->
    mongo_api:with_transaction(SessionPid, Topology,
        fun(_) -> process_data(Topology, Data) end)
end, DataList),

ok = mongo_api:end_session(SessionPid).
```

## Limitations

1. **Single Document Operations**: Don't need transactions for single document operations
2. **Cross-Shard Limitations**: Some operations may not be supported across shards
3. **Timeout Limits**: Transactions have maximum time limits (default 60 seconds)
4. **Memory Usage**: Large transactions consume more memory

## Troubleshooting

### Common Errors

1. **`{error, invalid_session}`**: Session has expired or been ended
2. **`{error, not_master}`**: Connected to secondary in replica set
3. **`{error, transaction_timeout}`**: Transaction exceeded time limit
4. **`{error, write_conflict}`**: Concurrent modification conflict

### Debugging

Enable logging to see transaction details:

```erlang
%% Set log level for debugging
logger:set_primary_config(level, debug).

%% Check transaction state
{ok, State} = mc_transaction:get_transaction_state(TransactionPid),
io:format("Transaction state: ~p~n", [State]).

%% Check session validity
Valid = mc_session:is_session_valid(SessionPid),
io:format("Session valid: ~p~n", [Valid]).
```

## Examples

See the test files for comprehensive examples:
- `test/eunit/transaction_test.erl` - Unit tests
- `test/mongo_transaction_SUITE.erl` - Integration tests
